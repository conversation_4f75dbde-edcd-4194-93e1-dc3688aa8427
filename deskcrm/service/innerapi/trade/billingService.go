package trade

import (
	"deskcrm/api/moat"
	"deskcrm/components"
	"deskcrm/consts"
	"deskcrm/controllers/http/ui/input/inputStudent"
	"deskcrm/controllers/http/ui/output/outputStudent"
	"fmt"
	"slices"
	"sort"
	"strconv"
	"strings"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type billingService struct{}

var (
	BillingService billingService
)

// GetStudentTradeList 获取学生订单列表
// 迁移自 PHP 的 AssistantDesk_Data_Student::getTradeList 方法
func (s billingService) GetStudentTradeList(ctx *gin.Context, studentUid int64, startTime, endTime int64) ([]TradeInfo, error) {
	// 分批获取所有订单数据
	allTradeList, err := s.fetchAllTradeData(ctx, studentUid, startTime, endTime)
	if err != nil {
		return nil, err
	}
	components.DebugfWithJSONAndCount(ctx, "GetStudentTradeList fetchAllTradeData total: %d, data: %s", len(allTradeList), allTradeList)

	// 处理订单数据
	resultTradeMap := make(map[int64]TradeInfo)
	s.processTradeList(ctx, allTradeList, resultTradeMap)
	result := make([]TradeInfo, 0, len(resultTradeMap))
	for _, trade := range resultTradeMap {
		result = append(result, trade)
	}
	components.DebugfWithJSONAndCount(ctx, "GetStudentTradeList processTradeList total: %d, data: %s", len(result), result)

	return result, nil
}

func (s billingService) GetAllTradeCourseList(ctx *gin.Context, studentUid int64) ([]TradeInfo, error) {
	// 获取学生订单信息（最近365天）
	orderBeginTime := time.Now().Unix() - 365*24*60*60
	orderEndTime := time.Now().Unix()

	// 分批获取所有订单数据
	allTradeList, err := s.fetchAllTradeData(ctx, studentUid, orderBeginTime, orderEndTime)
	if err != nil {
		return nil, err
	}
	components.DebugfWithJSONAndCount(ctx, "GetAllTradeCourseList fetchAllTradeData total: %d, data: %s", len(allTradeList), allTradeList)

	result := make([]TradeInfo, 0)
	for _, orderData := range allTradeList {
		// 处理每个订单的子订单列表
		for _, subItem := range orderData.SubList {
			if consts.IsValidPaidTradeStatus(subItem.Status) {
				// 构建并保存交易信息
				tradeInfo := s.buildTradeInfo(subItem)
				result = append(result, tradeInfo)
			}
		}
	}

	components.DebugfWithJSONAndCount(ctx, "GetAllTradeCourseList total: %d, data: %s", len(result), result)
	return result, nil
}

// fetchAllTradeData 分批获取所有订单数据
func (s billingService) fetchAllTradeData(ctx *gin.Context, studentUid, startTime, endTime int64) ([]TradeListResult, error) {
	var allTradeList []TradeListResult
	offset := 0
	limit := 100

	for {
		// 构建查询参数
		params := TradeListParams{
			UserId:         studentUid,
			OrderTimeStart: startTime,
			OrderTimeStop:  endTime,
			Offset:         offset,
			Limit:          limit,
			Fields:         []string{"subList.refundInfo"}, // 获取退款信息
		}

		// 调用订单查询接口
		tradeListResponse, err := s.tradeList(ctx, params)
		if err != nil {
			zlog.Errorf(ctx, "GetTradeList failed at offset %d: %v", offset, err)
			return nil, err
		}

		// 检查是否有数据返回
		if tradeListResponse == nil || len(tradeListResponse.List) == 0 {
			break // 没有更多数据，退出循环
		}

		currentBatchCount := len(tradeListResponse.List)
		zlog.Infof(ctx, "GetTradeList fetched batch: offset=%d, count=%d, total=%d",
			offset, currentBatchCount, tradeListResponse.Total)

		// 添加当前批次数据到总列表
		allTradeList = append(allTradeList, tradeListResponse.List...)

		// 如果已获取的数据量达到了总数，退出循环
		if len(allTradeList) >= tradeListResponse.Total {
			break
		}

		// 更新偏移量，准备获取下一批
		offset += limit
	}

	return allTradeList, nil
}

// processTradeList 处理订单数据
func (s billingService) processTradeList(ctx *gin.Context, allTradeList []TradeListResult, resultTradeMap map[int64]TradeInfo) {
	for _, orderData := range allTradeList {
		// 处理每个订单的子订单列表
		for _, subItem := range orderData.SubList {
			// 应用业务过滤规则
			if !s.isValidSubTrade(subItem, resultTradeMap) {
				components.DebugfWithJSON(ctx, "processTradeList isValidSubTrade false, data: %s", subItem)
				continue
			}

			// 检查是否需要更新现有记录（保留最新交易）
			courseId := subItem.CourseId
			if existingTrade, exists := resultTradeMap[courseId]; exists {
				if existingTrade.TradeTime >= subItem.TradeTime {
					components.DebugfWithJSON(ctx, "processTradeList existingTrade.TradeTime >= subItem.TradeTime, subItem: %s", subItem)
					continue // 已有更新的交易记录，跳过当前记录
				}
			}

			// 构建并保存交易信息
			tradeInfo := s.buildTradeInfo(subItem)
			resultTradeMap[courseId] = tradeInfo
		}
	}
}

// isValidSubTrade 检查子订单是否符合业务规则
func (s billingService) isValidSubTrade(subItem SubTradeItem, existingTrades map[int64]TradeInfo) bool {
	// 跳过未知状态的订单（对应PHP版本的 TRADE_STATUS_UNKNOWN 过滤）
	if subItem.Status == consts.TradeStatusUnknown {
		return false
	}

	// 跳过非有效状态的订单（只保留已支付相关状态）
	if !slices.Contains(consts.ValidTradeStatusList, subItem.Status) {
		return false
	}

	// 只处理课程类型的订单（ProductType = 1）
	if subItem.ProductType != consts.ProductTypeCourse {
		return false
	}

	// 跳过无效的课程ID
	if subItem.CourseId == 0 {
		return false
	}

	// 特殊处理：跳过金额为0且已存在的课程记录
	if subItem.TradeFee == 0 {
		if _, exists := existingTrades[subItem.CourseId]; exists {
			return false
		}
	}

	return true
}

// buildTradeInfo 构建交易信息对象
func (s billingService) buildTradeInfo(subItem SubTradeItem) TradeInfo {
	// 获取退款开始时间
	var refundStartTime int64 = 0
	if subItem.RefundInfo != nil {
		refundStartTime = subItem.RefundInfo.RefundStartTime
	}

	// 构建完整的交易信息（对应 PHP 返回的完整结构）
	return TradeInfo{
		SubTradeId:      subItem.SubTradeId,
		TradeId:         subItem.TradeId,
		SkuId:           subItem.SkuId,
		CourseId:        subItem.CourseId,
		ItemTag:         subItem.ItemTag,
		TradeFee:        subItem.TradeFee,
		TradeTime:       subItem.TradeTime,
		Status:          subItem.Status,
		RefundStartTime: refundStartTime,
	}
}

// tradeList 订单列表查询
func (s billingService) tradeList(ctx *gin.Context, params TradeListParams) (*TradeListResponse, error) {
	arrRet := &TradeListResponse{
		Total: 0,
		List:  []TradeListResult{},
	}

	// 订单渠道 => 店铺
	shopIds := s.getShopIds(params.OrderChannel)
	zlog.Infof(ctx, "billingService.tradeList - 步骤1: 订单渠道转店铺, orderChannel: %s, shopIds: %v, userId: %d, offset: %d, limit: %d",
		params.OrderChannel, shopIds, params.UserId, params.Offset, params.Limit)

	// 获取用户ID及订单ID
	oneClient := moat.NewClient()
	orderBy := []map[string]string{{"orderTime": "desc"}}

	offset := params.Offset
	if params.Page > 0 && params.PageSize > 0 {
		offset = (params.Page - 1) * params.PageSize
	}

	limit := params.Limit
	if params.PageSize > 0 {
		limit = params.PageSize
	}

	list, err := oneClient.GetTradeList(ctx, params.UserId, []int{},
		params.OrderTimeStart, params.OrderTimeStop, offset, limit, shopIds, orderBy)
	if err == nil {
		zlog.Infof(ctx, "billingService.tradeList - 步骤2: 获取用户ID及订单ID, userId: %d, shopIds: %v, list_total: %d, list_count: %d",
			params.UserId, shopIds, list.Total, len(list.List))
	}
	if err != nil {
		return nil, fmt.Errorf("订单检索失败: %w", err)
	}

	// 添加日志：GetTradeList调用结果
	components.DebugfWithJSONAndCount(ctx, "tradeList GetTradeList total: %d, data: %s", len(list.List), list)

	if len(list.List) == 0 {
		return arrRet, nil
	}

	arrRet.Total = list.Total

	// 获取订单详情
	orderIds := make([]int64, 0, len(list.List))
	for _, item := range list.List {
		orderIds = append(orderIds, item.OrderId)
	}

	tradeFields := []string{
		"userId", "orderId", "orderTime", "payTime", "businessType",
		"payableAmount", "paidAmount", "orderStatus", "orderBusinessStatus",
		"logInfo", "skuRowList", "bindDetail", "addressInfo",
		"discountInfo", "currencyType", "assembleDetail",
	}

	zlog.Infof(ctx, "billingService.tradeList - 步骤3: 获取订单详情, userId: %d, orderIds: %v, orderIds_count: %d, tradeFields: %v",
		params.UserId, orderIds, len(orderIds), tradeFields)
	detail, err := oneClient.GetTradeDetail(ctx, params.UserId, orderIds, tradeFields)
	if err != nil {
		return nil, fmt.Errorf("订单详情查询失败: %w", err)
	}

	// 添加日志：GetTradeDetail调用结果
	components.DebugfWithJSONAndCount(ctx, "tradeList GetTradeDetail total: %d, data: %s", len(detail), detail)

	if len(detail) == 0 {
		arrRet.Total = 0
		return arrRet, nil
	}

	// 处理组合品2.0订单
	assembleOrderIds := s.getAssembleOrderIds(detail)
	newOrderIds := make([]int64, 0)
	for _, assembleId := range assembleOrderIds {
		found := slices.Contains(orderIds, assembleId)
		if !found {
			newOrderIds = append(newOrderIds, assembleId)
		}
	}
	zlog.Infof(ctx, "billingService.tradeList - 步骤4: 处理组合品2.0订单, assembleOrderIds: %v, assembleOrderIds_count: %d, original_orderIds: %v, need_requery: %t",
		assembleOrderIds, len(assembleOrderIds), orderIds, len(newOrderIds) > 0)

	if len(assembleOrderIds) > 0 {
		if len(newOrderIds) > 0 {
			newDetail, err := oneClient.GetTradeDetail(ctx, params.UserId, newOrderIds, tradeFields)
			if err != nil {
				return nil, fmt.Errorf("组合品2.0-订单详情查询失败: %w", err)
			}

			if len(newDetail) == 0 {
				zlog.Warnf(ctx, "组合品2.0-没有查询到订单详情")
			}

			// 更新 orderIds、detail 为完整的订单列表
			orderIds = append(orderIds, newOrderIds...)
			detail = append(detail, newDetail...)
		}
	}

	// 获取新物流信息
	subTradeIdExpressInfoMap, err := s.getSubTradeIdExpressInfoMap(ctx, orderIds)
	if err != nil {
		zlog.Warnf(ctx, "获取物流信息失败: %v", err)
		// 物流信息获取失败不影响主流程，继续执行
	}
	zlog.Infof(ctx, "billingService.tradeList - 步骤5: 获取新物流信息, orderIds: %v, orderIds_count: %d, expressInfo_count: %d",
		orderIds, len(orderIds), len(subTradeIdExpressInfoMap))

	// 格式化订单数据
	zlog.Infof(ctx, "billingService.tradeList - 步骤6: 格式化订单数据, userId: %d, detail_count: %d, fields: %v, expressInfo_count: %d",
		params.UserId, len(detail), params.Fields, len(subTradeIdExpressInfoMap))
	formattedList, err := s.formatTradeList(ctx, params.UserId, detail, params.Fields, subTradeIdExpressInfoMap)
	if err != nil {
		return nil, fmt.Errorf("格式化订单数据失败: %w", err)
	}

	arrRet.List = formattedList
	return arrRet, nil
}

// getShopIds 将订单渠道转换为店铺ID列表
// 对应 PHP 的 Fudao_Billing::getShopIds 方法
func (s billingService) getShopIds(orderChannel string) []int64 {
	if orderChannel == "" {
		return []int64{}
	}

	tmp := strings.Split(orderChannel, ",")
	shopIds := make(map[int64]bool)

	for _, v := range tmp {
		val, err := strconv.ParseInt(strings.TrimSpace(v), 10, 64)
		if err != nil {
			continue
		}

		// 订单渠道0表示一课，店铺id是4
		if val == consts.OrderChannelYike {
			val = consts.ShopIdYike
		}

		shopIds[val] = true
	}

	result := make([]int64, 0, len(shopIds))
	for shopId := range shopIds {
		result = append(result, shopId)
	}

	return result
}

// getItemTag 根据产品ID和SKU服务类型生成商品标签
// 对应 PHP 的 Fudao_Billing::getItemTag 方法
func (s billingService) getItemTag(productId int64, skuServiceType int) string {
	switch skuServiceType {
	case consts.SkuServiceCourseType:
		return fmt.Sprintf("C_%d", productId)
	case consts.SkuServiceVirtualType:
		return fmt.Sprintf("V_%d", productId)
	case consts.SkuServiceEntityType:
		return fmt.Sprintf("R_%d", productId)
	case consts.SkuServiceThirdCoinType:
		return fmt.Sprintf("T_%d", productId)
	case consts.SkuServicePackageType:
		return fmt.Sprintf("P_%d", productId)
	}
	return ""
}

// formatTradeList 格式化订单列表数据
// 对应 PHP 的 Fudao_Billing::formatTradeList 方法
func (s billingService) formatTradeList(ctx *gin.Context, userId int64, detail []moat.TradeDetailItem, fields []string, subTradeIdExpressInfoMap map[int64]moat.ExpressInfo) ([]TradeListResult, error) {
	result := make([]TradeListResult, 0, len(detail))

	// 收集需要查询的数据ID (对应 PHP 的数据收集逻辑)
	afterIds := make(map[string]bool)
	courseIds := make(map[int64]bool)
	skuId2CurrencyType := make(map[int64]int)

	// 遍历订单详情收集ID
	for _, orderDetail := range detail {
		for _, skuRow := range orderDetail.SkuRowList {
			// 只有课程类型的商品才收集课程ID (对应 PHP 的 skuServiceType 判断)
			if skuRow.SkuServiceType == consts.SkuServiceCourseType {
				courseIds[skuRow.ProductId] = true
			}
			skuId2CurrencyType[skuRow.SkuId] = orderDetail.CurrencyType

			for _, item := range skuRow.AfterType {
				if afterTypeMap, ok := item.(map[string]interface{}); ok {
					for afterIdStr, afterTypeInterface := range afterTypeMap {
						if afterType, ok := afterTypeInterface.(float64); ok {
							if !consts.IsExcludedAfterType(int(afterType)) {
								afterIds[afterIdStr] = true
							}
						} else if afterType, ok := afterTypeInterface.(int); ok {
							if !consts.IsExcludedAfterType(afterType) {
								afterIds[afterIdStr] = true
							}
						}
					}
				}
			}
		}
	}

	// 构建 fields 映射 (对应 PHP 的 array_flip($fields))
	fieldsMap := make(map[string]bool)
	for _, field := range fields {
		fieldsMap[field] = true
	}

	// 获取售后信息 (对应 PHP 的售后信息查询)
	var arrAfterDetail map[string]AfterDetail
	var err error
	if fieldsMap["subList.refundInfo"] && len(afterIds) > 0 {
		arrAfterDetail, err = s.getAfterDetail(ctx, userId, afterIds)
		if err != nil {
			zlog.Warnf(ctx, "查询售后单详情失败: %v", err)
			return nil, fmt.Errorf("查询售后单详情失败: %w", err)
		}
	}

	// 构建物流状态映射 (对应 PHP 的 $skuRowIdExpressStatusMap)
	skuRowIdExpressStatusMap := s.buildSkuRowIdExpressStatusMap(detail)

	// 组合品物流记录 (对应 PHP 的 $tradeIds)
	tradeIds := make(map[int64]bool)
	for _, expressInfo := range subTradeIdExpressInfoMap {
		if len(expressInfo.OrderDetails) == 0 {
			continue
		}
		for _, orderInfo := range expressInfo.OrderDetails {
			tradeIds[orderInfo.SkuRowId] = true
		}
	}

	// 添加日志：开始处理订单详情
	components.DebugfWithJSONAndCount(ctx, "formatTradeList processing detail total: %d, data: %s", len(detail), detail)

	for _, orderDetail := range detail {
		orderId := orderDetail.OrderId

		// 构建基本订单信息
		tradeResult := TradeListResult{
			UserId:        orderDetail.UserId,
			TradeId:       orderId,
			TradeTime:     orderDetail.PayTime,
			CreateTime:    orderDetail.OrderTime,
			BusinessType:  orderDetail.BusinessType,
			TotalPayMoney: orderDetail.PayableAmount,
			LogInfo:       orderDetail.LogInfo,
			PromotionList: orderDetail.DiscountInfo,
			HasExpress:    0, // 子订单是否有物流
		}

		// 检查物流记录 (对应 PHP 的物流记录检查逻辑)
		for _, bindInfo := range orderDetail.BindDetail {
			if bindMap, ok := bindInfo.(map[string]interface{}); ok {
				if skuRowId, exists := bindMap["skuRowId"]; exists {
					if skuRowIdInt, ok := skuRowId.(int64); ok {
						if s.isQueryExpress(skuRowIdExpressStatusMap[skuRowIdInt]) {
							tradeResult.HasExpress = 1
							break
						}
					}
				}
			}
			// 组合品2.0物流信息
			if tradeIds[orderId] {
				tradeResult.HasExpress = 1
			}
		}

		// 处理子订单列表
		subList := make([]SubTradeItem, 0, len(orderDetail.SkuRowList))
		for _, skuRow := range orderDetail.SkuRowList {
			subTradeItem := s.processSkuRow(skuRow, tradeResult, orderDetail, fieldsMap, arrAfterDetail)
			subList = append(subList, subTradeItem)
		}

		tradeResult.SubList = subList
		result = append(result, tradeResult)
	}

	// 添加日志：formatTradeList最终结果
	components.DebugfWithJSONAndCount(ctx, "formatTradeList final result total: %d, data: %s", len(result), result)

	return result, nil
}

// processSkuRow 处理SKU行项目，转换为子订单项
func (s billingService) processSkuRow(skuRow moat.SkuRowItem, tradeResult TradeListResult, orderDetail moat.TradeDetailItem, fieldsMap map[string]bool, arrAfterDetail map[string]AfterDetail) SubTradeItem {
	// 获取订单状态，与PHP版本保持一致
	status := consts.GetTradeStatus(skuRow.OrderBusinessStatus, skuRow.RefundStatus)

	// 获取退款信息
	var refundInfo *RefundInfo
	if status == consts.TradeStatusRefunded || status == consts.TradeStatusRefunding {
		refundInfo = &RefundInfo{
			RefundStartTime: 0, // TODO: 从 skuRow 中获取退款开始时间
		}
	}

	subTradeItem := SubTradeItem{
		SubTradeId:  skuRow.SkuRowId,
		CourseId:    skuRow.ProductId,
		ProductType: skuRow.SkuMode,
		SkuId:       skuRow.SkuId,
		ItemTag:     s.getItemTag(skuRow.ProductId, skuRow.SkuServiceType),
		TradeFee:    skuRow.PaidAmount,
		Status:      status,
		TradeId:     tradeResult.TradeId,
		TradeTime:   tradeResult.TradeTime,
		AddressInfo: orderDetail.AddressInfo,
		RefundInfo:  refundInfo,
	}

	// 处理扩展字段 - 退款信息
	if fieldsMap["subList.refundInfo"] {
		subTradeItem.RefundInfo = s.getRefundInfo(arrAfterDetail, skuRow.AfterType)
	}

	return subTradeItem
}

// getAssembleOrderIds 获取组合品2.0订单ID列表
// 对应 PHP 的 Fudao_Billing::getAssembleOrderIds 方法
func (s billingService) getAssembleOrderIds(orderList []moat.TradeDetailItem) []int64 {
	if len(orderList) == 0 {
		return []int64{}
	}

	assembleIds := make(map[int64]bool)
	for _, orderInfo := range orderList {
		if len(orderInfo.AssembleDetail) == 0 {
			continue
		}

		for _, mainSkuRow := range orderInfo.AssembleDetail {
			if len(mainSkuRow.SkuRowList) == 0 {
				continue
			}

			for _, skuRow := range mainSkuRow.SkuRowList {
				if skuRow.OrderId > 0 {
					assembleIds[skuRow.OrderId] = true
				}
			}
		}
	}

	result := make([]int64, 0, len(assembleIds))
	for orderId := range assembleIds {
		result = append(result, orderId)
	}

	return result
}

// getSubTradeIdExpressInfoMap 获取子订单物流信息映射
// 对应 PHP 的 Fudao_Billing::getSubTradeIdExpressInfoMap 方法
func (s billingService) getSubTradeIdExpressInfoMap(ctx *gin.Context, orderIds []int64) (map[int64]moat.ExpressInfo, error) {
	if len(orderIds) == 0 {
		return map[int64]moat.ExpressInfo{}, nil
	}

	// 调用 Scpt API 获取物流信息
	scptClient := moat.NewClient()
	expressList, err := scptClient.Search(ctx, orderIds, []string{})
	if err != nil {
		zlog.Warnf(ctx, "Fudao_Billing.getSubTradeIdExpressInfoMap params: %v; error: %v", orderIds, err)
		return map[int64]moat.ExpressInfo{}, err
	}

	subTradeIdExpressInfoMap := make(map[int64]moat.ExpressInfo)
	for _, expressInfo := range expressList {
		if len(expressInfo.OrderDetails) == 0 {
			// 过滤无效数据
			continue
		}

		for _, orderDetail := range expressInfo.OrderDetails {
			subTradeId := orderDetail.SkuRowId
			subTradeIdExpressInfoMap[subTradeId] = expressInfo
		}
	}

	return subTradeIdExpressInfoMap, nil
}

// buildSkuRowIdExpressStatusMap 构建SKU行ID到物流状态的映射
// 对应 PHP 中的 $skuRowIdExpressStatusMap 逻辑
func (s billingService) buildSkuRowIdExpressStatusMap(detail []moat.TradeDetailItem) map[int64]int {
	skuRowIdExpressStatusMap := make(map[int64]int)

	for _, orderDetail := range detail {
		for _, skuRow := range orderDetail.SkuRowList {
			// 使用订单业务状态作为物流状态
			// 这样 isQueryExpress 方法可以正确判断是否需要查询物流
			skuRowIdExpressStatusMap[skuRow.SkuRowId] = skuRow.OrderBusinessStatus
		}
	}

	return skuRowIdExpressStatusMap
}

// isQueryExpress 判断是否需要查询物流信息
// 对应 PHP 的 Fudao_Billing::isQueryExpress 方法
func (s billingService) isQueryExpress(orderBusinessStatus int) bool {
	// 根据 PHP 代码的逻辑，只有以下状态需要查询物流
	switch orderBusinessStatus {
	case consts.OrderBusinessStatusToProduce, // 待发货
		consts.OrderBusinessStatusReceive,  // 待收货
		consts.OrderBusinessStatusFinished: // 已完成
		return true
	default:
		return false
	}
}

// getAfterDetail 获取售后详情
// 对应 PHP 的 Fudao_AfterPlat::afterDetail 调用
func (s billingService) getAfterDetail(ctx *gin.Context, userId int64, afterIds map[string]bool) (map[string]AfterDetail, error) {
	if len(afterIds) == 0 {
		return make(map[string]AfterDetail), nil
	}

	// 转换为数组
	afterIdList := make([]string, 0, len(afterIds))
	for afterId := range afterIds {
		afterIdList = append(afterIdList, afterId)
	}

	// 调用 AfterPlat API 获取售后信息
	option := []string{"refund"} // 获取退款信息
	afterPlatClient := moat.NewClient()
	afterPlatMap, err := afterPlatClient.AfterDetail(ctx, userId, afterIdList, option)
	if err != nil {
		zlog.Warnf(ctx, "moat.AfterDetail failed: %v", err)
		return nil, fmt.Errorf("moat.AfterDetail failed: %w", err)
	}

	// 转换为我们需要的格式
	result := make(map[string]AfterDetail)
	for afterId, afterPlatDetail := range afterPlatMap {
		// 从真实的 API 数据中获取退款信息
		refundStartTime := afterPlatDetail.RefundInfo.CreateTime
		refundableAmount := afterPlatDetail.RefundInfo.RefundGoodsAmount + afterPlatDetail.RefundInfo.RefundExpressAmount

		// 转换 AfterPlat API 的 AfterRefundInfo 为我们的 AfterRefundInfo
		ourRefundInfo := AfterRefundInfo{
			RefundId:            afterPlatDetail.RefundInfo.RefundId,
			RefundGoodsAmount:   afterPlatDetail.RefundInfo.RefundGoodsAmount,
			RefundExpressAmount: afterPlatDetail.RefundInfo.RefundExpressAmount,
			RefundDeductAmount:  afterPlatDetail.RefundInfo.RefundDeductAmount,
			Status:              afterPlatDetail.RefundInfo.Status,
			CreateTime:          afterPlatDetail.RefundInfo.CreateTime,
			UpdateTime:          afterPlatDetail.RefundInfo.UpdateTime,
		}

		result[afterId] = AfterDetail{
			AfterId:          afterPlatDetail.AfterId,
			UserId:           afterPlatDetail.UserId,
			OrderId:          afterPlatDetail.OrderId,
			Status:           afterPlatDetail.Status,
			CreateTime:       afterPlatDetail.CreateTime,
			RefundInfo:       ourRefundInfo,
			RefundStartTime:  refundStartTime,
			RefundableAmount: refundableAmount,
		}
	}

	// 对于没有查询到的售后单，填充默认值
	for _, afterId := range afterIdList {
		if _, exists := result[afterId]; !exists {
			result[afterId] = AfterDetail{
				RefundStartTime:  0, // 默认值
				RefundableAmount: 0, // 默认值
			}
		}
	}

	return result, nil
}

// getRefundInfo 获取退款信息
// 对应 PHP 的 self::getRefundInfo($arrAfterDetail, array_keys($vv['afterType'])) 逻辑
// 多个服务单金额相加，退费时间取最早的一条
func (s billingService) getRefundInfo(arrAfterDetail map[string]AfterDetail, afterTypes []interface{}) *RefundInfo {
	if afterTypes == nil || len(arrAfterDetail) == 0 {
		return nil
	}

	var refundStartTime int64 = 0
	var refundableAmount int64 = 0
	hasRefundInfo := false

	for _, item := range afterTypes {
		if afterTypeMap, ok := item.(map[string]interface{}); ok {
			for afterId := range afterTypeMap {
				if afterDetail, exists := arrAfterDetail[afterId]; exists {
					hasRefundInfo = true
					s.processRefundDetail(&refundStartTime, &refundableAmount, afterDetail)
				}
			}
		}
	}

	if !hasRefundInfo {
		return nil
	}

	return &RefundInfo{
		RefundStartTime:  refundStartTime,
		RefundableAmount: refundableAmount,
	}
}

// processRefundDetail 处理单个退款详情
func (s billingService) processRefundDetail(refundStartTime *int64, refundableAmount *int64, afterDetail AfterDetail) {
	// 退费时间取最早的一单的时间 (对应 PHP 逻辑)
	if *refundStartTime == 0 {
		*refundStartTime = afterDetail.RefundStartTime
	} else if afterDetail.RefundStartTime > 0 && afterDetail.RefundStartTime < *refundStartTime {
		*refundStartTime = afterDetail.RefundStartTime
	}

	// 退费金额相加 (对应 PHP 的 refundGoodsAmount + refundExpressAmount)
	*refundableAmount += afterDetail.RefundInfo.RefundGoodsAmount
	*refundableAmount += afterDetail.RefundInfo.RefundExpressAmount
}

// GetStudentOrderList 获取学生订单列表
func (s billingService) GetStudentOrderList(ctx *gin.Context, param *inputStudent.StudentOrderListParam) (result outputStudent.StudentOrderListOutput, err error) {
	// 1. 获取订单列表数据
	orderListData, err := s.fetchStudentOrderListWithStats(ctx, param)
	if err != nil {
		zlog.Errorf(ctx, "fetchStudentOrderListWithStats failed: %v", err)
		return result, err
	}

	// 添加日志：fetchStudentOrderListWithStats调用结果
	components.DebugfWithJSONAndCount(ctx, "GetStudentOrderList fetchStudentOrderListWithStats total: %d, data: %s", len(orderListData.List), orderListData)

	// 2. 获取总金额信息
	allMoney, err := s.getStudentAllMoney(ctx, param.UserId)
	if err != nil {
		zlog.Warnf(ctx, "getStudentAllMoney failed: %v", err)
		// 总金额获取失败不影响主流程，设为0
		allMoney = 0
	}

	result.List = orderListData.List
	result.Total = orderListData.Total
	result.SkuNumber = orderListData.SkuNumber
	result.RefundNumber = orderListData.RefundNumber
	result.AllMoney = allMoney

	return result, nil
}

// fetchStudentOrderListWithStats 获取学生订单列表数据（包含统计信息）
// 对应 PHP 的 Api_StudentLogisticsInfo::getStudentOrderList 完整逻辑
func (s billingService) fetchStudentOrderListWithStats(ctx *gin.Context, param *inputStudent.StudentOrderListParam) (result outputStudent.StudentOrderListOutput, err error) {
	var allData []outputStudent.StudentOrderItem

	// 获取所有订单数据
	orderList, err := moat.NewClient().GetOneOpenList(ctx, param.UserId, []int64{}, 0)
	if err != nil {
		zlog.Errorf(ctx, "GetOneOpenList failed: %v", err)
		return result, err
	}

	// 添加日志：GetOneOpenList调用结果
	components.DebugfWithJSONAndCount(ctx, "fetchStudentOrderListWithStats GetOneOpenList total: %d, data: %s", len(orderList), orderList)

	// 收集订单ID和SKU ID用于后续查询（对应 PHP 的 $skuIds[] = $sku['skuId']）
	orderIds := make([]int64, 0, len(orderList))
	allSkuIds := make([]int64, 0)
	for _, item := range orderList {
		orderIds = append(orderIds, item.OrderId)
		// 收集SKU ID
		for _, sku := range item.SkuRowList {
			allSkuIds = append(allSkuIds, sku.SkuId)
		}
	}

	// 获取物流信息
	expressInfo, err := s.getExpressInfo(ctx, orderIds)
	if err != nil {
		zlog.Errorf(ctx, "getExpressInfo failed: %v", err)
		return result, err
	}

	// 添加日志：getExpressInfo调用结果
	components.DebugfWithJSONAndCount(ctx, "fetchStudentOrderListWithStats getExpressInfo total: %d, data: %s", len(expressInfo), expressInfo)

	allExpressInfo := make(map[int64]moat.ExpressInfo)
	for k, v := range expressInfo {
		allExpressInfo[k] = v
	}

	// 转换订单数据格式
	for _, orderItem := range orderList {
		convertedItem := s.convertOneOpenListToStudentOrderItem(orderItem, allExpressInfo)
		allData = append(allData, convertedItem)
	}

	// 获取售后信息
	afterList, err := moat.NewClient().SearchAfterPlat(ctx, param.UserId, orderIds)
	if err != nil {
		zlog.Warnf(ctx, "SearchAfterPlat failed: %v", err)
		return result, err
	}

	// 添加日志：SearchAfterPlat调用结果
	components.DebugfWithJSONAndCount(ctx, "fetchStudentOrderListWithStats SearchAfterPlat total: %d, data: %s", len(afterList), afterList)

	// 构建售后信息映射
	afterOrderDetails := make(map[int64]AfterOrderInfo)
	for _, afterInfo := range afterList {
		afterOrderDetails[afterInfo.OrderId] = AfterOrderInfo{
			CreateTime: afterInfo.CreateTime,
			Status:     afterInfo.Status,
		}
	}

	// 应用退款状态过滤和售后信息
	filteredData, refundCount := s.applyRefundFilterWithStatsAndDetails(allData, afterOrderDetails, param.RefundStatus)

	// 按orderTime降序排序（对应PHP版本的排序逻辑）
	sort.Slice(filteredData, func(i, j int) bool {
		return filteredData[i].OrderTime > filteredData[j].OrderTime
	})

	// 应用分页（对应 PHP 的用户分页逻辑）
	result.Total = len(filteredData)
	result.SkuNumber = len(allSkuIds)
	result.RefundNumber = refundCount
	result.List = s.applyPagination(filteredData, param.Page, param.PageSize)

	return result, nil
}

// getStudentAllMoney 获取学生总金额
// 对应 PHP 的 Service_Page_DeskV1_Student_CourseRecordMeta::execute 方法
func (s billingService) getStudentAllMoney(ctx *gin.Context, studentUid int64) (allMoney float64, err error) {
	// 获取学生的所有订单，时间范围：最近一年（与 PHP 版本保持一致）
	now := time.Now().Unix()
	startTime := now - 365*86400
	endTime := now

	allTradeList, err := s.fetchAllTradeData(ctx, studentUid, startTime, endTime)
	if err != nil {
		return
	}

	// 添加日志：fetchAllTradeData调用结果
	components.DebugfWithJSONAndCount(ctx, "getStudentAllMoney fetchAllTradeData total: %d, data: %s", len(allTradeList), allTradeList)

	for _, trade := range allTradeList {
		for _, subTrade := range trade.SubList {
			// 只计算已支付状态的订单和当前课程
			if consts.IsValidPaidTradeStatus(subTrade.Status) {
				allMoney += float64(subTrade.TradeFee)
			}
		}
	}

	// 添加日志：getStudentAllMoney计算结果
	components.Debugf(ctx, "getStudentAllMoney calculation result: %f", allMoney)

	return allMoney, nil
}

// getExpressInfo 获取物流信息
// 对应 PHP 的 Fudao_Scpt::Search 调用
func (s billingService) getExpressInfo(ctx *gin.Context, orderIds []int64) (map[int64]moat.ExpressInfo, error) {
	result := make(map[int64]moat.ExpressInfo)

	if len(orderIds) == 0 {
		return result, nil
	}

	expressList, err := moat.NewClient().Search(ctx, orderIds, []string{})
	if err != nil {
		zlog.Warnf(ctx, "Search express info failed: %v", err)
		return result, err
	}

	// 添加日志：Search调用结果
	components.DebugfWithJSONAndCount(ctx, "getExpressInfo Search total: %d, data: %s", len(expressList), expressList)

	// 转换物流信息格式
	for _, expressInfo := range expressList {
		if len(expressInfo.OrderDetails) == 0 {
			continue
		}

		for _, orderDetail := range expressInfo.OrderDetails {
			subTradeId := orderDetail.SkuRowId
			result[subTradeId] = expressInfo
		}
	}

	// 添加日志：getExpressInfo最终结果
	components.DebugfWithJSONAndCount(ctx, "getExpressInfo final result total: %d, data: %s", len(result), result)

	return result, nil
}

// convertOneOpenListToStudentOrderItem 转换 OneOpenListItem 为 StudentOrderItem
// 对应 PHP 的订单数据格式化逻辑
func (s billingService) convertOneOpenListToStudentOrderItem(orderItem moat.OneOpenListItem, expressInfo map[int64]moat.ExpressInfo) outputStudent.StudentOrderItem {
	item := outputStudent.StudentOrderItem{
		OrderId:     orderItem.OrderId,
		OrderTime:   orderItem.OrderTime,
		PayTime:     orderItem.PayTime,
		AfterTime:   0,                             // 初始化为0，后续会根据售后信息更新
		Status:      orderItem.OrderBusinessStatus, // 对应 PHP 的 $item['status'] = $orderItem['orderBusinessStatus']
		AfterStatus: 0,                             // 初始化为0，后续会根据售后信息更新
		StatusName:  "",                            // 初始化为空，后续会根据状态计算
		SkuRowList:  make([]outputStudent.StudentOrderSkuRow, 0, len(orderItem.SkuRowList)),
	}

	// 转换SKU行数据
	for _, skuRow := range orderItem.SkuRowList {
		skuItem := outputStudent.StudentOrderSkuRow{
			SkuName:     skuRow.SkuName,
			IsRefund:    0,                             // 默认无退款，后续会根据售后信息更新
			OrderStatus: orderItem.OrderBusinessStatus, // 使用订单的业务状态
			GoodsAmount: float64(skuRow.GoodsAmount),
			PaidAmount:  float64(skuRow.PaidAmount),
			SubTradeId:  skuRow.SkuRowId,
			ShopId:      int64(skuRow.ShopId),
		}

		// 添加物流信息
		if express, exists := expressInfo[skuRow.SkuRowId]; exists {
			skuItem.ExpressInfo = &express
		}

		item.SkuRowList = append(item.SkuRowList, skuItem)
	}

	return item
}

// applyRefundFilterWithStatsAndDetails 应用退款状态过滤（带统计信息和详细售后信息）
// 对应 PHP 的退款状态过滤逻辑，同时返回退款数量统计
func (s billingService) applyRefundFilterWithStatsAndDetails(orderList []outputStudent.StudentOrderItem, afterOrderDetails map[int64]AfterOrderInfo, refundStatus int) ([]outputStudent.StudentOrderItem, int) {
	var result []outputStudent.StudentOrderItem
	refundCount := 0

	// 去重
	indexedByOrderId := make(map[int64]outputStudent.StudentOrderItem)
	for _, order := range orderList {
		indexedByOrderId[order.OrderId] = order
	}

	for _, order := range indexedByOrderId {
		// 更新订单的退款状态
		hasRefund := false
		if _, exists := afterOrderDetails[order.OrderId]; exists {
			hasRefund = true
			refundCount++
		}

		// 更新售后相关字段（对应 PHP 的售后信息设置）
		if hasRefund {
			if afterDetail, exists := afterOrderDetails[order.OrderId]; exists {
				order.AfterTime = afterDetail.CreateTime
				order.AfterStatus = afterDetail.Status
			} else {
				order.AfterTime = 0
				order.AfterStatus = 1 // 默认售后状态
			}
		} else {
			order.AfterTime = 0
			order.AfterStatus = 0
		}

		// 计算状态名称（对应 PHP 的 getStatusName 方法）
		order.StatusName = consts.GetTradeStatusName(order.Status, order.AfterStatus, order.AfterTime)

		// 更新SKU行的退款状态
		for i := range order.SkuRowList {
			if hasRefund {
				order.SkuRowList[i].IsRefund = 1
			} else {
				order.SkuRowList[i].IsRefund = 0
			}
		}

		// 根据退款状态过滤（对应 PHP 的 onlyRefundStatus 和 onlyUnRefundStatus 逻辑）
		switch refundStatus {
		case 0: // 全部
			result = append(result, order)
		case 1: // 只看售后（对应 PHP 的 onlyRefundStatus）
			if hasRefund {
				result = append(result, order)
			}
		case 2: // 只看未售后（对应 PHP 的 onlyUnRefundStatus）
			if !hasRefund {
				result = append(result, order)
			}
		}
	}

	return result, refundCount
}

// applyPagination 应用分页
// 对应 PHP 的用户分页逻辑
func (s billingService) applyPagination(orderList []outputStudent.StudentOrderItem, page, pageSize int) []outputStudent.StudentOrderItem {
	total := len(orderList)
	if total == 0 {
		return []outputStudent.StudentOrderItem{}
	}

	start := (page - 1) * pageSize
	if start >= total {
		return []outputStudent.StudentOrderItem{}
	}

	end := min(start+pageSize, total)

	return orderList[start:end]
}
