<?php
/**
 * billing迁移方案：https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=376108444
 *
 * <AUTHOR>
 * Date: 2022/11/07 11:48
 */
class Fudao_Billing
{
    // 以下售后服务单类型不获取退费时间
    private static $arrExcludeAfterType = [
        Sp_Dict_Trade_AllAfterType::DEFINE_REFUND_ONLY => 0, // 仅退款【自定义】
        Sp_Dict_Trade_AllAfterType::PRICE_SUBSIDY => 0, // 价补
    ];

    /**
     * 分页查询订单列表，按下单时间降序返回
     *
     * 适配/billing/trade/listpro接口 https://yapi.zuoyebang.cc/project/233/interface/api/42184
     *
     * @param int $userId 用户id
     * @param int $status 订单状态 -1全部，1已支付 注意：只支持传-1或1（已支付，包含已退款）
     * @param array $shopIds 店铺id
     * @param int $startTime 订单创建时间 e.g 1669351578
     * @param int $endTime 订单结束时间 e.g 1669351999
     * @param int $page 页码，默认1
     * @param int $pageSize 每页条数，默认100，最多100
     * @param string $orderChannel http://yapi.zuoyebang.cc/project/233/interface/api/76739
     * @param array $fields 会请求单独的接口获取的字段，不需要建议不要传。若获取失败函数会返回false
     * 暂支持以下字段：
     *   subList.skuInfo
     *     skuName
     *     courseType
     *       rmbPriceInfo
     *         price
     *         oriPrice
     *         discountPrice
     *   subList.refundInfo 退费信息
     *     refundStartTime 退款发起时间
     *     refundableAmount 本次退商品支付金额（单位：分）+ 本次退运费金额
     *
     * @return false|array
     */
    public static function tradeList($userId, $status = -1, $startTime = 0, $endTime = 0, $page = 1, $pageSize = 100, $orderChannel = '0', $fields = []) {
        // 将dar订单状态映射成订单检索接口的状态
        // billing listpro status = 1 https://git.zuoyebang.cc/zb/phplib/-/blob/dev/const/Trade.php#L26-29
        // listpro里入参里的status1和dar订单状态1（已支付未退款）不一样，这里包含下列状态
        $orderBusinessStatusList = [];
        if (1 == $status) {
            $orderBusinessStatusList = [
                Sp_Dict_Trade_OrderBusinessStatus::PAID,
                Sp_Dict_Trade_OrderBusinessStatus::FULFILLING,
                Sp_Dict_Trade_OrderBusinessStatus::TO_PRODUCE,
                Sp_Dict_Trade_OrderBusinessStatus::RECEIVE,
                Sp_Dict_Trade_OrderBusinessStatus::FINISHED,
                Sp_Dict_Trade_OrderBusinessStatus::CANCELED,
                Sp_Dict_Trade_OrderBusinessStatus::PAUSE,
            ];
        }
        // 订单渠道 => 店铺
        $shopId = self::getShopIds($orderChannel);
        Bd_Log::notice("Fudao_Billing::tradeList - 步骤1: 订单渠道转店铺", 0, [
            'orderChannel' => $orderChannel,
            'shopId' => $shopId,
            'userId' => $userId,
            'status' => $status,
            'startTime' => $startTime,
            'endTime' => $endTime,
            'page' => $page,
            'pageSize' => $pageSize
        ]);

        // 获取用户ID及订单ID
        $list = Fudao_One::getTradeList($userId, $orderBusinessStatusList, $startTime, $endTime, ($page -1) * $pageSize, $pageSize, $shopId, [['orderTime' => 'desc']]);
        Bd_Log::notice("Fudao_Billing::tradeList - 步骤2: 获取用户ID及订单ID", 0, [
            'userId' => $userId,
            'orderBusinessStatusList' => $orderBusinessStatusList,
            'shopId' => $shopId,
            'list_total' => isset($list['data']['total']) ? $list['data']['total'] : 0,
            'list_count' => isset($list['data']['list']) ? count($list['data']['list']) : 0
        ]);
        if (false === $list) {
            Bd_Log::warning('订单检索失败');
            return false;
        }

        $arrRet = [
            'total' => 0,
            'list' => [],
            'logId' => $list['logId'],
        ];
        if (0 !== $list['errNo']) {
            return false;
        }
        $list = $list['data'] ?? [];
        if ((0 >= $list['total']) || empty($list['list'])) {
            return $arrRet;
        }
        $arrRet['total'] = $list['total'];

        // 获取订单详情
        $orderIds = array_column($list['list'] ?? [], 'orderId');
        $tradeFields = ['userId', 'orderId', 'orderTime', 'payTime', 'businessType', 'payableAmount', 'paidAmount', 'orderStatus', 'orderBusinessStatus', 'logInfo', 'skuRowList', 'bindDetail', 'addressInfo', 'discountInfo', 'currencyType', 'assembleDetail'];
        Bd_Log::notice("Fudao_Billing::tradeList - 步骤3: 获取订单详情", 0, [
            'userId' => $userId,
            'orderIds' => $orderIds,
            'orderIds_count' => count($orderIds),
            'tradeFields' => $tradeFields
        ]);
        $detail = Fudao_One::getTradeDetail($userId, $orderIds, $tradeFields);
        if (false === $detail) {
            Bd_Log::warning('订单详情查询失败');
            return false;
        }
        if (0 !== $detail['errNo']) {
            return false;
        }
        if (empty($detail['data'])) {
            Bd_Log::warning('没有查询到订单详情??');
            $arrRet['total'] = 0;
            return $arrRet;
        }

        // 处理组合品2.0订单
        $assembleOrderIds = self::getAssembleOrderIds($detail['data']);
        Bd_Log::notice("Fudao_Billing::tradeList - 步骤4: 处理组合品2.0订单", 0, [
            'assembleOrderIds' => $assembleOrderIds,
            'assembleOrderIds_count' => count($assembleOrderIds),
            'original_orderIds' => $orderIds,
            'need_requery' => count(array_diff($assembleOrderIds, $orderIds)) > 0
        ]);
        if (count(array_diff($assembleOrderIds, $orderIds)) > 0) {
            // 重新获取组合品2.0订单
            $orderIds = array_values(array_unique(array_merge($assembleOrderIds, $orderIds)));
            $detail = Fudao_One::getTradeDetail($userId, $orderIds, $tradeFields);
            if (false === $detail) {
                Bd_Log::warning('组合品2.0-订单详情查询失败');
                return false;
            }
            if (0 !== $detail['errNo']) {
                return false;
            }
            if (empty($detail['data'])) {
                Bd_Log::warning('组合品2.0-没有查询到订单详情??');
                $arrRet['total'] = 0;
                return $arrRet;
            }
        }

        // 获取新物流信息
        $subTradeIdExpressInfoMap = self::getSubTradeIdExpressInfoMap($orderIds);
        Bd_Log::notice("Fudao_Billing::tradeList - 步骤5: 获取新物流信息", 0, [
            'orderIds' => $orderIds,
            'orderIds_count' => count($orderIds),
            'expressInfo_count' => count($subTradeIdExpressInfoMap)
        ]);

        Bd_Log::notice("Fudao_Billing::tradeList - 步骤6: 格式化订单数据", 0, [
            'userId' => $userId,
            'detail_count' => count($detail['data']),
            'fields' => $fields,
            'expressInfo_count' => count($subTradeIdExpressInfoMap)
        ]);
        $mixedRet = self::formatTradeList($userId, $detail['data'], $fields, $subTradeIdExpressInfoMap);
        if (false === $mixedRet) {
            return false;
        }
        $arrRet['list'] = $mixedRet;

        return $arrRet;
    }

    public static function getAssembleOrderIds($orderList) {
        if (empty($orderList)) {
            return [];
        }

        $assembleIds = [];
        foreach ($orderList as $orderInfo) {
            if (empty($orderInfo['assembleDetail'])) {
                continue;
            }

            foreach ($orderInfo['assembleDetail'] as $mainSkuRow) {
                if (empty($mainSkuRow['skuRowList'])) {
                    continue;
                }

                foreach ($mainSkuRow['skuRowList'] as $skuRow) {
                    if ($skuRow['orderId'] > 0) {
                        $assembleIds[$skuRow['orderId']] = $skuRow['orderId'];
                    }
                }
            }
        }

        return $assembleIds;
    }

    /**
     * 获取历史订单（2.0）
     * getHistoryTradeList
     * @param
     * @param
     * @return false|array
     */
    public static function getHistoryTradeList($userId, $page, $pageSize, $orderChannel = '0') {
        $shopIds = self::getShopIds($orderChannel);

        $historyOrderList = Fudao_One::getHistoryTradeList($userId, $page, $pageSize);
        if (false === $historyOrderList) {
            Bd_Log::warning('订单检索失败');
            return false;
        }

        foreach($historyOrderList['list'] as $key => $historyOrderInfo) {
            if(!in_array($historyOrderInfo['shopId'], $shopIds)) {
                unset($historyOrderList['list'][$key]);
                $historyOrderList['total']--;
            }
        }

        $historyOrderList['list'] = self::formatHistoryTradeList($userId, $historyOrderList['list']);

        return $historyOrderList;
    }

    private static function formatHistoryTradeList($userId, $historyOrderlist) {
        $orderList = [];
        $skuIds = [];
        foreach($historyOrderlist as $historyOrderInfo) {
            foreach($historyOrderInfo['skuList'] as $skuInfo) {
                $skuIds[] = $skuInfo['skuId'];
            }
        }
        if (empty($skuIds)) {
            return $orderList;
        }

        $skuIdCourseIdMap = [];
        $skuInfoMap = Fudao_GoodsPlatform::getGoodsSkuKVBySkuId($skuIds);
        foreach($skuInfoMap as $skuInfo) {
            $courseIdMap[$skuInfo['thirdId']] = 0;
            $skuIdCourseIdMap[$skuInfo['skuId']] = $skuInfo['thirdId'];
        }

        if (!empty($courseIds)) {
            $arrCourse = Fudao_Dal::getCourseBaseByCourseIds(array_keys($courseIdMap), ['courseId', 'newCourseType']);
            if (false === $arrCourse) {
                Bd_Log::warning('dal查询课程信息失败');
                return false;
            }
        }

        foreach($historyOrderlist as $key => $historyOrderInfo) {
            $subList = [];
            foreach($historyOrderInfo['skuList'] as $skuInfo) {
                $subList[] = [
                    'subTradeId'  => $skuInfo['skuOrderId'],
                    'courseId'    => $skuIdCourseIdMap[$skuInfo['skuId']],
                    'productType' => $skuInfo['productType'],
                    'skuId'       => $skuInfo['skuId'],
                    'itemTag'     => '',
                    'tradeFee'    => $skuInfo['refundFee'],
                    'status'      => $skuInfo['status'],
                    'tradeId'     => $skuInfo['orderId'],
                    'tradeTime'   => $historyOrderInfo['orderTime'],
                    'addressInfo' => $skuInfo['addressInfo'],
                    'skuInfo'     => [
                        'skuName'    => $skuInfo['skuName'],
                        'courseType' => $arrCourse[$skuInfo['productId']]['newCourseType'],
                        'rmbPriceInfo' => [
                            'price' => $historyOrderInfo['goodsAmount'],
                            'oriPrice' => $historyOrderInfo['goodsAmount'],
                            'discountPrice' => $historyOrderInfo['goodsAmount'],
                        ],
                    ],
                    'refundInfo'  => $skuInfo['refundInfo'],
                ];
            }
            $orderList[$key] = [
                'userId'        => $historyOrderInfo['userId'],
                'tradeId'       => $historyOrderInfo['orderId'],
                'tradeTime'     => $historyOrderInfo['orderTime'],
                'hasExpress'    => '', //未使用
                'totalPayMoney' => $historyOrderInfo['payableAmount'],
                'subList'       => $subList,
            ];
        }

        return $orderList;
    }


    /**
     *
     *
     * @param $userId
     * @param $detail
     * @param $fields
     * @param array $subTradeIdExpressInfoMap
     * @return false|array
     */
    private static function formatTradeList($userId, $detail, $fields, $subTradeIdExpressInfoMap = []) {
        $arrRet = [];
        $afterIds = [];
        $courseIds = [];
        $skuId2CurrencyType = [];

        $skuRowIdExpressStatusMap = [];
        foreach ($detail as $k => $v) {
            foreach ($v['skuRowList'] as $kk => $vv) {
                $skuRowIdExpressStatusMap[$vv['skuRowId']] = $vv['orderBusinessStatus'];

                if (Sp_Dict_Goods_SkuServiceType::SKU_SERVICE_COURSE_TYPE == $vv['skuServiceType']) {
                    $courseIds[$vv['productId']] = 0;
                }
                // 退款信息
                if (!empty($vv['afterType'])) {
                    foreach ($vv['afterType'] as $id => $type) {
                        if (!isset(self::$arrExcludeAfterType[$type])) {
                            $afterIds[$id] = 0;
                        }
                    }
                }
                $skuId2CurrencyType[$vv['skuId']] = $v['currencyType'];
            }
        }

        $arrCourse = [];
        $arrAfterDetail = [];
        $arrSkuInfo = [];
        $fields = array_flip($fields);
        if (isset($fields['subList.skuInfo'])) {
            if (!empty($courseIds)) {
                $arrCourse = Fudao_Dal::getCourseBaseByCourseIds(array_keys($courseIds), ['courseId', 'newCourseType']);
                if (false === $arrCourse) {
                    Bd_Log::warning('dal查询课程信息失败');
                    return false;
                }
            }
            $arrSkuInfo = self::getSkuInfo($skuId2CurrencyType);
            if (false === $arrSkuInfo) {
                return false;
            }
        }
        // 售后信息
        if (isset($fields['subList.refundInfo']) && !empty($afterIds)) {
            $arrAfterDetail = Fudao_AfterPlat::afterDetail($userId, array_keys($afterIds), ['refund']);
            if (false === $arrAfterDetail) {
                Bd_Log::warning('查询售后单详情失败');
                return false;
            }
        }

        // 组合品物流记录
        $tradeIds = [];
        foreach ($subTradeIdExpressInfoMap as $expressInfo) {
            if (empty($expressInfo['orderDetails'])) {
                continue;
            }

            foreach ($expressInfo['orderDetails'] as $orderInfo) {
                $tradeIds[$orderInfo['orderId']] = $orderInfo['orderId'];
            }
        }

        foreach($detail as $k => $v) {
            $orderId = $v['orderId'];
            $tmp = [
                'userId' => $v['userId'],
                'tradeId' => $orderId,
                'tradeTime' => $v['payTime'],
                'hasExpress' => 0, // 子订单是否有物流
                'totalPayMoney' => $v['payableAmount'],
                'subList' => [],
            ];

            // 物流记录
            foreach ($v['bindDetail'] as $bindInfo) {
                if (self::isQueryExpress($skuRowIdExpressStatusMap[$bindInfo['skuRowId']])) {
                    $tmp['hasExpress'] = 1;
                    break;
                }
                // 组合品2.0物流信息
                if (!empty($tradeIds[$orderId])) {
                    $tmp['hasExpress'] = 1;
                }
            }

            foreach ($v['skuRowList'] as $kk => $vv) {
                $skuId = $vv['skuId'];
                $skuRowId = $vv['skuRowId'];
                $row = [
                    'subTradeId' => $skuRowId,
                    'courseId' => $vv['productId'],
                    'productType' => $vv['skuMode'],
                    'skuId' => $skuId,
                    'itemTag' => self::getItemTag($vv['productId'], $vv['skuServiceType']),
                    'tradeFee' => $vv['paidAmount'],
                    // TODO 这里不过滤的话，有异常的status怎么处理？
                    'status' => Fudao_Dar::getTradeStatus($vv['orderBusinessStatus'], $vv['refundStatus']),
                    // 以下字段取自订单维度
                    'tradeId' => $orderId,
                    'tradeTime' => $v['payTime'],
                    'addressInfo' => self::getAddressInfo($v['addressInfo'] ?? []),
                ];
                if (isset($fields['subList.skuInfo'])) {
                    $price = $arrSkuInfo['currency'][$skuId]['price'] ?? 0;
                    $originPrice = $arrSkuInfo['currency'][$skuId]['originPrice'] ?? 0;
                    $row['skuInfo'] = [
                        'skuName' => $vv['skuName'],
                        'courseType' => $arrCourse[$vv['productId']]['newCourseType'],
                        'rmbPriceInfo' => [
                            'price' => $price,
                            'oriPrice' => $originPrice,
                            'discountPrice' => $originPrice - $price,
                        ],
                    ];
                }
                // 售后
                if (isset($fields['subList.refundInfo'])) {
                    $row['refundInfo'] = self::getRefundInfo($arrAfterDetail, array_keys($vv['afterType']));
                }

                $tmp['subList'][] = $row;
            }

            $arrRet[$v['orderId']] = $tmp;
        }

        return $arrRet;
    }

    /**
     * 根据用户ID、订单IDs查询订单详情
     * 不支持子订单、source及businessSystem
     *
     * @param int $userId 用户id
     * @param int $tradeId 订单id
     * @param array $fields 会请求单独的接口获取的字段，不需要建议不要传。若获取失败函数会返回false
     * 暂支持：
     *   expressInfo 物流信息
     *   subList.skuInfo
     *     skuName
     *     courseType
     *   subList.refundInfo 退费信息
     *     refundStartTime 退款发起时间
     *     refundableAmount 本次退商品支付金额（单位：分）+ 本次退运费金额
     *
     * @return false|array
     */
    public static function tradeDetail($userId, $tradeId, $fields = []) {
        $orderIds = [$tradeId];
        $detailFields = ['userId', 'orderId', 'orderTime', 'payTime', 'businessType', 'payableAmount', 'orderBusinessStatus', 'addressInfo', 'skuRowList', 'discountInfo', 'bindDetail', 'orderVersion', 'logInfo', 'assembleDetail'];
        $detail = Fudao_One::getTradeDetail($userId, $orderIds, $detailFields);
        if (false === $detail) {
            Bd_Log::warning('订单详情查询失败');
            return false;
        }

        $arrRet = [];
        if (0 !== $detail['errNo']) {
            return false;
        }
        if (empty($detail['data'])) {
            return $arrRet;
        }

        // 组合品2.0订单
        $assembleOrderIds = self::getAssembleOrderIds($detail['data']);
        if (count(array_diff($assembleOrderIds, [$tradeId])) > 0) {
            // 重新获取组合品2.0订单
            $assembleOrderIds[$tradeId] = $tradeId;
            $orderIds = array_values($assembleOrderIds);
            $detail = Fudao_One::getTradeDetail($userId, $orderIds, $detailFields);
            if (false === $detail) {
                Bd_Log::warning('组合品2.0-订单详情查询失败');
                return false;
            }
            if (0 !== $detail['errNo']) {
                return false;
            }
            if (empty($detail['data'])) {
                Bd_Log::warning('组合品2.0-没有查询到订单详情??');
                return $arrRet;
            }
        }

        // 获取新物流信息
        $subTradeIdExpressInfoMap = self::getSubTradeIdExpressInfoMap($orderIds);

        return self::formatTradeDetail($userId, $detail['data'], $fields, $subTradeIdExpressInfoMap);
    }

    public static function getSubTradeIdExpressInfoMap($orderIds) {
        if (empty($orderIds)) {
            return [];
        }

        $errMsg = '';
        $expressList = Fudao_Scpt::Search($orderIds, [], $errMsg);
        if ($expressList === false) {
            Bd_Log::warning("Fudao_Billing.getSubTradeIdExpressInfoMap params: " . json_encode($orderIds) . "; expressList: " . json_encode($expressList));
            return [];
        }

        $subTradeIdExpressInfoMap = [];
        foreach ($expressList as $expressInfo) {
            if (empty($expressInfo['orderDetails'])) {
                // 过滤无效数据
                continue;
            }

            foreach ($expressInfo['orderDetails'] as $orderDetail) {
                $subTradeId = $orderDetail['skuRowId'];

                $subTradeIdExpressInfoMap[$subTradeId] = $expressInfo;
            }
        }
        return $subTradeIdExpressInfoMap;
    }

    /**
     * 订单明细
     *
     * 将教辅和赠品拆开返回
     * @param $userId
     * @param array $detail
     * @param array $fields
     * @param array $subTradeIdExpressInfoMap
     * @return false|array
     */
    private static function formatTradeDetail($userId, $detail, $fields, $subTradeIdExpressInfoMap = []) {
        $arrRet = [];
        $afterIds = [];
        $courseIds = [];

        $skuRowIdExpressStatusMap = [];
        foreach ($detail as $k => $v) {
            foreach ($v['skuRowList'] as $kk => $vv) {
                $skuRowIdExpressStatusMap[$vv['skuRowId']] = $vv['orderBusinessStatus'];

                if (Sp_Dict_Goods_SkuServiceType::SKU_SERVICE_COURSE_TYPE == $vv['skuServiceType']) {
                    $courseIds[$vv['productId']] = 0;
                }
                // 退款信息
                if (!empty($vv['afterType'])) {
                    foreach ($vv['afterType'] as $id => $type) {
                        if (!isset(self::$arrExcludeAfterType[$type])) {
                            $afterIds[$id] = 0;
                        }
                    }
                }
            }
        }

        $arrCourse = [];
        $arrAfterDetail = [];
        $arrExpressInfo = [];
        $fields = array_flip($fields);
        if (isset($fields['subList.skuInfo']) && !empty($courseIds)) {
            $arrCourse = Fudao_Dal::getCourseBaseByCourseIds(array_keys($courseIds), ['courseId', 'newCourseType']);
            if (false === $arrCourse) {
                Bd_Log::warning('dal课程信息查询失败');
                return false;
            }
        }
        // 售后信息
        if (isset($fields['subList.refundInfo']) && !empty($afterIds)) {
            $arrAfterDetail = Fudao_AfterPlat::afterDetail($userId, array_keys($afterIds), ['refund']);
            if (false === $arrAfterDetail) {
                Bd_Log::warning('afterplat售后单详情失败');
                return false;
            }
        }
        // 物流
        $expressIds = [];
        $skuRowIdExpressKeyMap = [];
        foreach ($detail as $k => $v) {
            if (empty($v['bindDetail'])) {
                continue;
            }

            $skuRowIdBindDetailMap = array_column($v['bindDetail'], 'bindSkuRowList', 'skuRowId');
            if (empty($skuRowIdBindDetailMap)) {
                continue;
            }

            foreach ($skuRowIdBindDetailMap as $skuRowId => $bindSkuRowList) {
                if (empty($bindSkuRowList)) {
                    continue;
                }

                if (self::isQueryExpress($skuRowIdExpressStatusMap[$skuRowId])) {
                    foreach ($bindSkuRowList as $bind) {
                        if (!isset($bind['orderId'])) {
                            continue;
                        }
                        $expressIds[$bind['orderId']] = 0;
                        $skuRowIdExpressKeyMap[$skuRowId][] = sprintf('%s_%s', $bind['orderId'], $bind['skuRowId']);
                    }
                }
            }
        }
        if (isset($fields['expressInfo']) && !empty($expressIds)) {
            $mixedRet = Fudao_Wms::expressStatusOrderObtain($userId, array_keys($expressIds));
            if (false === $mixedRet) {
                Bd_Log::warning('wms物流详情查询失败');
                return false;
            }
            foreach ($mixedRet as $tradeId => $v) {
                $arrExpressInfo = array_merge($arrExpressInfo, self::buildOldWmsInfoData($tradeId, $v));
            }
        }

        foreach ($detail as $k => $v) {
            $tradeId = $v['orderId'];
            $orderVersion = $v['orderVersion'];
            $tmp = [
                'userId' => $v['userId'],
                'tradeId' => $tradeId,
                'tradeTime' => $v['payTime'],
                'createTime' => $v['orderTime'],
                'businessType' => $v['businessType'],
                'totalPayMoney' => $v['payableAmount'],
                'logInfo' => $v['logInfo'],
            ];
            // 优惠策略
            $tmp['promotionList'] = self::getPromotionList($v['discountInfo']['bizDetail'] ?? []);
            // 教辅
            $materialSubTrade = [];
            foreach ($v['bindDetail'] as $kk => $vv) {
                foreach ($vv['bindSkuRowList'] as $kkk => $vvv) {
                    $materialSubTrade[$vvv['skuRowId']] = [
                        'value' => [],
                        'subTradeId' => $vv['skuRowId'], // 课程订单ID
                    ];
                }
            }

            $subList = $giftList = [];
            foreach ($v['skuRowList'] as $kk => $vv) {
                $subTradeId = $vv['skuRowId'];

                $row = [
                    'subTradeId' => $subTradeId,
                    'courseId' => $vv['productId'],
                    'productType' => $vv['skuMode'],
                    'productName' => $vv['skuName'],
                    'skuId' => $vv['skuId'],
                    'itemTag' => self::getItemTag($vv['productId'], $vv['skuServiceType']),
                    'tradeFee' => $vv['paidAmount'],
                    // TODO 这里不过滤的话，有待支付、已支付、已退款之外的status怎么处理？
                    'status' => Fudao_Dar::getTradeStatus($v['orderBusinessStatus'], $v['refundStatus']),
                    'resendSubTradeId' => $vv['extData']['resendSkuRowId'] ?? 0,
                    'bindCourseSubTradeIds' => [],
                    'isPresentCourse' => $vv['isGift'],
                    'entityType' => self::getEntityType($vv['skuServiceType'], $vv['isGift']),
                    // 以下字段取自订单维度
                    'tradeId' => $tradeId,
                    'tradeTime' => $v['payTime'],
                    'addressInfo' => self::getAddressInfo($v['addressInfo'] ?? []),
                ];
                // skuInfo
                if (isset($fields['subList.skuInfo'])) {
                    $row['skuInfo'] = [
                        'skuName' => $vv['skuName'],
                        'courseType' => $arrCourse[$vv['productId']]['newCourseType'],
                    ];
                }
                // 售后
                if (isset($fields['subList.refundInfo'])) {
                    $row['refundInfo'] = self::getRefundInfo($arrAfterDetail, array_keys($vv['afterType']));
                }
                // 物流
                if (isset($fields['expressInfo'])) {
                    $expressList = [];
                    foreach ($skuRowIdExpressKeyMap[$subTradeId] ?? [] as $expressKey) {
                        $expressList[] = $arrExpressInfo[$expressKey];
                    }
                    $row['expressInfo'] = $expressList;
                    // 若没有数据，则从新包裹接口获取
                    if (empty($row['expressInfo'])) {
                        $row['expressInfo'][] = self::buildNewPackInfoData($subTradeId, $subTradeIdExpressInfoMap[$subTradeId]);
                    }
                }
                if (isset($materialSubTrade[$subTradeId])) {
                    $row['bindCourseSubTradeIds'][] = $materialSubTrade[$subTradeId]['subTradeId'];
                }

                // 拆分教辅、赠课、赠品（包括实物等）列表
                if (self::needSplitMaterialSubTrade($orderVersion)) {
                    if (isset($materialSubTrade[$subTradeId])) {
                        $materialSubTrade[$subTradeId]['value'] = $row;
                    } else if (1 === intval($vv['isGift'])) {
                        $row['materialSubTrade'] = [];
                        $giftList[$subTradeId] = $row;
                    } else {
                        $row['materialSubTrade'] = [];
                        $subList[$subTradeId] = $row;
                    }
                } else {
                    $row['materialSubTrade'] = [];
                    $subList[$subTradeId] = $row;
                }
            }

            if (self::needSplitMaterialSubTrade($orderVersion)) {
                foreach ($materialSubTrade as $k => $v) {
                    if (empty($v['value'])) {
                        continue;
                    }

                    $subTradeId = $v['subTradeId'];
                    if (isset($subList[$subTradeId])) {
                        $subList[$subTradeId]['materialSubTrade'][] = $v['value'];
                    }
                    if (isset($giftList[$subTradeId])) {
                        $giftList[$subTradeId]['materialSubTrade'][] = $v['value'];
                    }
                }
            }

            $tmp['subList'] = array_values($subList);
            $tmp['giftList'] = array_values($giftList);
            $arrRet[$tradeId] = $tmp;
        }

        return $arrRet;
    }

    public static function buildNewPackInfoData($subTradeId, $expressInfo) {
        $subTradeIdInfoMap = array_column($expressInfo['orderDetails'], null, 'skuRowId');
        $currentSubTradeInfo = $subTradeIdInfoMap[$subTradeId] ?? [];

        return [
            'uid' => isset($currentSubTradeInfo['uid']) ? $currentSubTradeInfo['uid'] : 0,
            'tradeId' => isset($currentSubTradeInfo['orderId']) ? $currentSubTradeInfo['orderId'] : 0,
            'subTradeId' => isset($currentSubTradeInfo['skuRowId']) ? $currentSubTradeInfo['skuRowId'] : 0,
            'receiverAddress' => isset($expressInfo['receiverAddress']) ? $expressInfo['receiverAddress'] : '',
            'receiverName' => isset($expressInfo['receiverName']) ? $expressInfo['receiverName'] : '',
            'receiverPhone' => isset($expressInfo['receiverPhone']) ? $expressInfo['receiverPhone'] : '',
            'sendStatus' => isset($expressInfo['statusName']) ? $expressInfo['statusName'] : '',
            'sendDesc' => isset($expressInfo['statusFlows']) ? $expressInfo['statusFlows'] : [],
            'sendType' => isset($expressInfo['sendType']) ? $expressInfo['sendType'] : '',
            'sendTime' => isset($expressInfo['sendTime']) ? $expressInfo['sendTime'] : 0,
            'expressNumber' => isset($expressInfo['expressNumber']) ? $expressInfo['expressNumber'] : '',
            'status' => isset($expressInfo['status']) ? $expressInfo['status'] : 0,
            'productList' => [[
                "productName" => $currentSubTradeInfo['skuName'],
			    "productCnt" => $currentSubTradeInfo['quantity'],
		    ]],
            //'estimateMsg' => isset($wmsOrderInfo['estimateMsg']) ? $wmsOrderInfo['estimateMsg'] : '', //预计发货&预计送达时间
            //'estimateSendStartTime' => isset($wmsOrderInfo['estimateSendStartTime']) ? $wmsOrderInfo['estimateSendStartTime'] : '', //预计最早发货时间 时间戳格式
            //'estimateSendEndTime' => isset($wmsOrderInfo['estimateSendEndTime']) ? $wmsOrderInfo['estimateSendEndTime'] : '', //预计最晚发货时间 时间戳格式
            //'estimateArriveTime' => isset($wmsOrderInfo['estimateArriveTime']) ? $wmsOrderInfo['estimateArriveTime'] : '', //预计送达时间 时间戳格式
            //'isOneBill' => isset($wmsOrderInfo['isOneBill']) ? $wmsOrderInfo['isOneBill'] : 0, //是否合单
            'packTime' => isset($expressInfo['packTime']) ? $expressInfo['packTime'] : 0, //打包时间
            //'canRemind' => isset($wmsOrderInfo['canRemind']) ? $wmsOrderInfo['canRemind'] : 0, //能否催单(0不能 1能)
            //'remindMsg' => isset($wmsOrderInfo['remindMsg']) ? $wmsOrderInfo['remindMsg'] : '', //催单状态文案，能否催单原因展示（已经催过单了，只有待发货订单可以催单，可以催单）
        ];
    }

    /**
     * 3.0之前的订单需要将教辅、赠品分开返回
     * 3.0有拆单逻辑，无需分开返回
     *
     * @param
     * @return bool
     */
    private static function needSplitMaterialSubTrade($orderVersion) {
        $arrOrderVersion = [
            Sp_Dict_Trade_OrderVersion::ORDER_VERSION_ODC => 0,
            Sp_Dict_Trade_OrderVersion::ORDER_VERSION_ASC => 0,
            Sp_Dict_Trade_OrderVersion::ORDER_VERSION_DAR => 0,
        ];

        return isset($arrOrderVersion[$orderVersion]) ? true : false;
    }

    /**
     * 根据子订单支付状态及是否是实物判断是否需要查物流
     * @param $orderBusinessStatus
     * @return bool
     */
    public static function isQueryExpress($orderBusinessStatus) {
        $status = [
            Sp_Dict_Trade_OrderBusinessStatus::TO_PRODUCE => 0, // 待发货
            Sp_Dict_Trade_OrderBusinessStatus::RECEIVE => 0, // 待收货
            Sp_Dict_Trade_OrderBusinessStatus::FINISHED => 0, // 已完成
        ];
        return isset($status[$orderBusinessStatus]);
    }

    /**
     * 根据子订单支付状态及是否是实物判断是否需要查物流
     *
     * @param array $skuRowList
     * @return bool
     */
    public static function isNeedQueryExpress($skuRowList) {
        if (empty($skuRowList)) {
            return false;
        }

        $status = [
            Sp_Dict_Trade_OrderBusinessStatus::TO_PRODUCE => 0, // 待发货
            Sp_Dict_Trade_OrderBusinessStatus::RECEIVE => 0, // 待收货
            Sp_Dict_Trade_OrderBusinessStatus::FINISHED => 0, // 已完成
        ];
        foreach ($skuRowList as $row) {
            // 物流 已支付、实物才可能有物流
            if ((Sp_Dict_Goods_SkuMode::PF_ENTITY_TYPE == $row['skuMode'])
                && isset($status[$row['orderBusinessStatus']])
            ) {
                return true;
            }
        }

        return false;
    }

    private static function getSkuInfo($skuId2CurrencyType) {
        if (empty($skuId2CurrencyType)) {
            return [];
        }
        $mixedRet = Fudao_GoodsPlatform::getGoodsSkuKVBySkuId(array_keys($skuId2CurrencyType));
        if (false === $mixedRet) {
            return false;
        }

        $arrRet = [];
        foreach ($mixedRet as $skuId => $v) {
            foreach ($v['currency'] as $kk => $vv) {
                if ($skuId2CurrencyType[$skuId] == $vv['type']) {
                    $arrRet[$skuId] = [
                        'currency' => $vv,
                    ];
                    break;
                }
            }
        }

        return $arrRet;
    }

    /**
     * orderChannel转店铺
     *
     * @param string $orderChannel
     * @return array
     */
    private static function getShopIds($orderChannel) {
        if (empty($orderChannel)) {
            return [];
        }

        $tmp = explode(',', $orderChannel);
        $tmp = array_map('intval', $tmp);
        foreach ($tmp as $k => $v) {
            // 订单渠道0表示一课，店铺id是4
            if (0 === $v) {
                $v = 4;
            }

            $shopId[$v] = 0;
        }

        return array_keys($shopId);
    }

    /**
     * 地址
     */
    public static function getAddressInfo($addressInfo) {
        if (empty($addressInfo)) {
            return [];
        }

        $arrRet = [];
        $items = ['phone', 'name', 'address', 'province', 'city', 'prefecture', 'town'];
        foreach ($items as $item) {
            $arrRet[$item] = $addressInfo[$item] ?? '';
        }

        return $arrRet;
    }

    /**
     * 优惠信息
     *
     * @param
     * @param
     * @return
     */
    private static function getPromotionList($bizDetail) {
        $arrRet = [];

        foreach ($bizDetail as $v) {
            $arrRet[] = [
                'name' => $v['mktName'],
                'totalDiscount' => $v['discountAmount'],
            ];
        }

        return $arrRet;
    }

    private static function getItemTag($productId, $skuServiceType) {
        $skuServiceType = intval($skuServiceType);

        switch ($skuServiceType) {
            case Sp_Dict_Goods_SkuServiceType::SKU_SERVICE_COURSE_TYPE:
                return sprintf('C_%s', $productId);

            case Sp_Dict_Goods_SkuServiceType::SKU_SERVICE_ENTITY_TYPE:
                return sprintf('R_%s', $productId);
        }

        return '';
    }

    /**
     * 实体类型
     *
     * @param
     * @param
     * @return
     */
    private static function getEntityType($skuServiceType, $isGift) {
        $isGift = intval($isGift);
        $skuServiceType = intval($skuServiceType);

        if (1 === $isGift) {
            return Zb_Const_Trade::ENTITY_TYPE_GIFT;
        }
        switch ($skuServiceType) {
            case Sp_Dict_Goods_SkuServiceType::SKU_SERVICE_COURSE_TYPE:
                return Zb_Const_Trade::ENTITY_TYPE_COURSE;

            case Sp_Dict_Goods_SkuServiceType::SKU_SERVICE_ENTITY_TYPE:
                return Zb_Const_Trade::ENTITY_TYPE_MATERIAL;
        }

        return -1;
    }

    /**
     * 退费详情
     *
     * 多个服务单金额相加，退费时间取最早的一条
     *
     * @param
     * @param
     * @return
     */
    private static function getRefundInfo($afterDetail, $afterIds) {
        if (empty($afterDetail) || empty($afterIds)) {
            return [];
        }

        $arrRet = [];
        foreach ($afterIds as $id) {
            if (isset($afterDetail[$id])) {
                $refundInfo = $afterDetail[$id]['refundInfo'];

                // 退费时间取最早的一单的时间
                if (!isset($arrRet['refundStartTime'])) {
                    $arrRet['refundStartTime'] = $refundInfo['createTime'] ?? 0;
                }
                if ($refundInfo['createTime'] < $arrRet['refundStartTime']) {
                    $arrRet['refundStartTime'] = $refundInfo['createTime'] ?? 0;
                }
                // 退费金额相加
                $arrRet['refundableAmount'] += $afterDetail[$id]['refundInfo']['refundGoodsAmount'] ?? 0;
                $arrRet['refundableAmount'] += $afterDetail[$id]['refundInfo']['refundExpressAmount'] ?? 0;
            }
        }

        return $arrRet;
    }

    /**
     * copy from billing Dao_Wms_ExpressInfo
     *
     * @param
     * @param
     * @return
     */
    public static function buildOldWmsInfoData($tradeId, $ret, $productId=0) {
        $wmsOrderInfoList = $ret['orderInfo'];
        $wmsExpressInfoList = $ret['expressFlows'];
        $retData = [];

        foreach ($wmsOrderInfoList as $subTradeId => $wmsOrderInfoss) {
            foreach ($wmsOrderInfoss as $wmsOrderInfos) {
                foreach ($wmsOrderInfos as $wmsOrderInfo) {
                    $productList = [];
                    $materialIds = [];
                    foreach ($wmsOrderInfo['materialDetails'] as $materialDetail) {
                        $mId = isset($materialDetail['materialId']) ? $materialDetail['materialId'] : 0;
                        if ($mId>0) {
                            $materialIds[] = $mId;
                        }
                        $productList[] = [
                            'productName' => isset($materialDetail['materialName']) ? $materialDetail['materialName'] : '',
                            'productCnt' => isset($materialDetail['quantity']) ? $materialDetail['quantity'] : 0,
                        ];
                    }

                    if ($productId>0 && !empty($materialIds)) {
                        if (!in_array($productId, $materialIds)) {
                            continue;
                        }
                    }
                    $packId = $wmsOrderInfo['packId'];
                    $expressInfo = $wmsExpressInfoList[$packId];

                    $oldWmsInfo = [
                        'uid' => isset($wmsOrderInfo['uid']) ? $wmsOrderInfo['uid'] : 0,
                        'packId' => isset($wmsOrderInfo['packId']) ? $wmsOrderInfo['packId'] : 0,
                        'tradeId' => isset($wmsOrderInfo['pid']) ? $wmsOrderInfo['pid'] : 0,
                        'subTradeId' => isset($wmsOrderInfo['orderId']) ? $wmsOrderInfo['orderId'] : 0,
                        'receiverAddress' => isset($wmsOrderInfo['receiverAddress']) ? $wmsOrderInfo['receiverAddress'] : '',
                        'receiverName' => isset($wmsOrderInfo['receiverName']) ? $wmsOrderInfo['receiverName'] : '',
                        'receiverPhone' => isset($wmsOrderInfo['receiverPhone']) ? $wmsOrderInfo['receiverPhone'] : '',
                        'sendStatus' => isset($wmsOrderInfo['statusName']) ? $wmsOrderInfo['statusName'] : '',
                        'sendDesc' => isset($expressInfo['statusFlows']) ? $expressInfo['statusFlows'] : [],
                        'sendType' => isset($wmsOrderInfo['sendType']) ? $wmsOrderInfo['sendType'] : '',
                        'sendTime' => isset($wmsOrderInfo['sendTime']) ? $wmsOrderInfo['sendTime'] : 0,
                        'expressNumber' => isset($wmsOrderInfo['expressNumber']) ? $wmsOrderInfo['expressNumber'] : '',
                        'status' => isset($wmsOrderInfo['status']) ? $wmsOrderInfo['status'] : 0,
                        'productList' => $productList,
                        'expectTime' => isset($wmsOrderInfo['expectTime']) ? $wmsOrderInfo['expectTime'] : [], // 废弃字段
                        'estimateMsg' => isset($wmsOrderInfo['estimateMsg']) ? $wmsOrderInfo['estimateMsg'] : '', //预计发货&预计送达时间
                        'estimateSendStartTime' => isset($wmsOrderInfo['estimateSendStartTime']) ? $wmsOrderInfo['estimateSendStartTime'] : '', //预计最早发货时间 时间戳格式
                        'estimateSendEndTime' => isset($wmsOrderInfo['estimateSendEndTime']) ? $wmsOrderInfo['estimateSendEndTime'] : '', //预计最晚发货时间 时间戳格式
                        'estimateArriveTime' => isset($wmsOrderInfo['estimateArriveTime']) ? $wmsOrderInfo['estimateArriveTime'] : '', //预计送达时间 时间戳格式
                        'isOneBill' => isset($wmsOrderInfo['isOneBill']) ? $wmsOrderInfo['isOneBill'] : 0, //是否合单
                        'packTime' => isset($wmsOrderInfo['packTime']) ? $wmsOrderInfo['packTime'] : 0, //打包时间
                        'canRemind' => isset($wmsOrderInfo['canRemind']) ? $wmsOrderInfo['canRemind'] : 0, //能否催单(0不能 1能)
                        'remindMsg' => isset($wmsOrderInfo['remindMsg']) ? $wmsOrderInfo['remindMsg'] : '', //催单状态文案，能否催单原因展示（已经催过单了，只有待发货订单可以催单，可以催单）
                    ];

                    $key = $tradeId . '_' . $subTradeId;
                    $retData[$key] = $oldWmsInfo;
                }
            }
        }

        return $retData;
    }
}
